<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Form</class>
 <widget class="QWidget" name="Form">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <widget class="QQuickWidget" name="quickWidget">
   <property name="geometry">
    <rect>
     <x>60</x>
     <y>40</y>
     <width>300</width>
     <height>200</height>
    </rect>
   </property>
   <property name="resizeMode">
    <enum>QQuickWidget::SizeRootObjectToView</enum>
   </property>
  </widget>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QQuickWidget</class>
   <extends>QWidget</extends>
   <header>QtQuickWidgets/QQuickWidget</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
