name: Upload Python Package

on:
  push:
    # Sequence of patterns matched against refs/tags
    tags:
      - v[0-9]+.[0-9]+.*  # add .* to allow dev releases

permissions:
  id-token: write  # Required for trusted publishing
  contents: write  # Required for release creation
jobs:
  deploy:

    name: pipenv PyPI Upload
    runs-on: ubuntu-latest
    env:
      CI: "1"

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Create Release
      id: create_release
      uses: ncipollo/release-action@v1
      with:
        tag: ${{ github.ref }}
        name: Release ${{ github.ref_name }}
        draft: false
        prerelease: false
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up Python 3.10
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"

    - name: Install dependencies
      run: |
        python -m pip install --upgrade --upgrade-strategy=eager pip setuptools build wheel twine
        python -m pip install -e . --upgrade
        python -m pipenv install --dev
      env:
        PIPENV_DEFAULT_PYTHON_VERSION: "3.10"

    - name: Build wheels
      run: |
        python -m pipenv run python -m build

    # Publish to PyPI using token authentication
    # Note: To use Trusted Publishers in the future, you need to set up a publisher on PyPI:
    # Visit: https://pypi.org/manage/project/pipenv/settings/publishing/
    # And add a publisher with:
    #   - Provider: GitHub
    #   - Owner: pypa
    #   - Repository: pipenv
    #   - Workflow filename: pypi_upload.yml
    - name: Publish a Python distribution to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        user: __token__
        password: ${{ secrets.PYPI_TOKEN }}
        packages-dir: dist/
        verbose: true
        # Uncomment the following line to publish to Test PyPI instead
        # repository-url: https://test.pypi.org/legacy/
