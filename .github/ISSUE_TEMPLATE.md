If you have problems to get your application running, please first

* try the latest development version, using the following command:

```shell
pip install https://github.com/pyinstaller/pyinstaller/archive/develop.zip
```

* follow *all* the instructions in our "If Things Go Wrong" Guide
  (https://github.com/pyinstaller/pyinstaller/wiki/If-Things-Go-Wrong) and

* make sure everything is packaged correctly
  https://github.com/pyinstaller/pyinstaller/wiki/How-to-Report-Bugs#make-sure-everything-is-packaged-correctly

If your problem persists, please provide all information as stated in our "How to Report Bugs" guide.
https://github.com/pyinstaller/pyinstaller/wiki/How-to-Report-Bugs.
The help for GitHub's text-editor is hidden behind "Styling with <PERSON><PERSON> is
supported" just below the edit-box.


**Otherwise we will not be willing to help you.**

Please be aware that PyInstaller is a voluntary project. We spent quite some time
writing these guides to make both of our lives easier. Please
understand that we are not willing to spend our spare time to help you if you
are not willing to help on your side. (Sorry for having to said this, but we
are tired of being asked the same questions over and over again.)
