.. _support:

Support
=======

.. image:: https://farm5.staticflickr.com/4198/34080352913_5c13ffb336_k_d.jpg

If you have questions or issues about Requests, there are several options:

Stack Overflow
-------------

If your question does not contain sensitive (possibly proprietary)
information or can be properly anonymized, please ask a question on
`Stack Overflow <https://stackoverflow.com/questions/tagged/python-requests>`_
and use the tag ``python-requests``.

Send a Tweet
------------

If your question is less than 280 characters, feel free to send a tweet to
`@kennethreitz <https://twitter.com/kennethreitz>`_,
`@sigmavirus24 <https://twitter.com/sigmavirus24>`_,
`@lukasaoz <https://twitter.com/lukasaoz>`_, or
`@nateprewitt <https://twitter.com/nateprewitt>`_.

File an Issue
-------------

If you notice some unexpected behaviour in Requests, or want to see support
for a new feature,
`file an issue on GitHub <https://github.com/requests/requests/issues>`_.


E-mail
------

I'm more than happy to answer any personal or in-depth questions about
Requests. Feel free to email
`<EMAIL> <mailto:<EMAIL>>`_.


IRC
---

The official Freenode channel for Requests is
`#python-requests <irc://irc.freenode.net/python-requests>`_

The core developers of requests are on IRC throughout the day.
You can find them in ``#python-requests`` as:

- kennethreitz
- lukasa
- sigmavirus24
