# Ways To Contribute/ What We Need Help With

## Help Others!

If you would like to help others who are just starting to contribute to Pinax, you can do the following:

* Hang out in [Slack](http://slack.pinaxproject.com) and help answer questions!
* Watch the [Pinax repositories](https://github.com/pinax) for issues or pull request you could help with!
* When you encounter a bug/have an idea for a feature/encounter missing documentation that could easily be fixed, create `first-timers-only` issues like [this one](https://github.com/pinax/pinax/issues/102) for people wanting to make their first OSS contribution! If you need help creating this sort of issue, please ping @KatherineMichel on GitHub or @katherinemichel in Slack.


## Contribute To The Community!

* Write blog posts [like these](http://blog.pinaxproject.com/section/how-tos/)!
* Host a [Pinax Hangout](https://www.youtube.com/channel/UCAPpNG85GLzUBwzYCjd4raQ)!
* Help us contribute to our existing documentation and write new documentation! You can find links to each app’s/starter project’s documentation in the README of the app/starter project.
* Write tutorials! If there’s an app or starter project you know well, write a tutorial and show others how to use it, what features it has, etc.


## Contribute Code!

* Write tests!
* If you have time to fix any of our GitHub issues (especially those labelled `up-for-grabs`) we would greatly appreciate your help!
* Create a new starter project which showcases how to use any of our Pinax apps (or a combination of them). This doesn’t have to be anything new or original. You could for example pick an existing website like Facebook and try to rebuild it with Pinax components.
* Write new Pinax apps! If while building your starter project you notice that an app is missing we would love for you to let us know about your idea or build the app yourself.
