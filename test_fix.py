#!/usr/bin/env python3
"""
Quick test to verify our package name determination fixes work correctly.
"""

import tempfile
import os
from pathlib import Path
from io import BytesIO

# Add the current directory to Python path so we can import pipenv modules
import sys
sys.path.insert(0, '.')

from pipenv.utils.dependencies import find_package_name_from_filename, find_package_name_from_directory

def test_filename_matching():
    """Test that our filename matching works correctly."""
    print("Testing filename matching...")
    
    # Test content that would be in a setup.py
    setup_content = b'''
from setuptools import setup

setup(
    name="test-package",
    version="1.0.0",
)
'''
    
    # Test 1: Full path from archive (should work)
    file_obj = BytesIO(setup_content)
    result1 = find_package_name_from_filename('some/path/setup.py', file_obj)
    print(f"✓ Full path test: {result1}")
    assert result1 == "test-package", f"Expected 'test-package', got {result1}"
    
    # Test 2: Basename from directory (should work)
    file_obj = BytesIO(setup_content)
    result2 = find_package_name_from_filename('setup.py', file_obj)
    print(f"✓ Basename test: {result2}")
    assert result2 == "test-package", f"Expected 'test-package', got {result2}"
    
    # Test 3: test_setup.py (should NOT match)
    file_obj = BytesIO(setup_content)
    result3 = find_package_name_from_filename('some/path/test_setup.py', file_obj)
    print(f"✓ test_setup.py test: {result3}")
    assert result3 is None, f"Expected None, got {result3}"
    
    # Test 4: my_setup.py (should NOT match)
    file_obj = BytesIO(setup_content)
    result4 = find_package_name_from_filename('my_setup.py', file_obj)
    print(f"✓ my_setup.py test: {result4}")
    assert result4 is None, f"Expected None, got {result4}"
    
    print("All filename matching tests passed!")

def test_directory_search_order():
    """Test that directory search order prioritizes parent directories."""
    print("\nTesting directory search order...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create package structure
        package_dir = temp_path / "mypackage"
        package_dir.mkdir()
        
        # Create main setup.py (should be found first)
        main_setup = package_dir / "setup.py"
        main_setup.write_text('''
from setuptools import setup
setup(name="correct-package", version="1.0.0")
''')
        
        # Create nested directory with confusing setup.py
        nested_dir = package_dir / "mypackage" / "tests"
        nested_dir.mkdir(parents=True)
        
        # Create test_setup.py (should be ignored due to exact matching)
        test_setup = nested_dir / "test_setup.py"
        test_setup.write_text('''
def setup(name=''):
    return f'Setup package {name}'

def configure():
    return setup(name='wrong-package')
''')
        
        # Test directory search
        result = find_package_name_from_directory(str(package_dir))
        print(f"✓ Directory search result: {result}")
        assert result == "correct-package", f"Expected 'correct-package', got {result}"
    
    print("Directory search order test passed!")

if __name__ == "__main__":
    test_filename_matching()
    test_directory_search_order()
    print("\n🎉 All tests passed! The fix is working correctly.")
